"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@noble";
exports.ids = ["vendor-chunks/@noble"];
exports.modules = {

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_blake.js":
/*!**************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_blake.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BSIGMA: () => (/* binding */ BSIGMA),\n/* harmony export */   G1s: () => (/* binding */ G1s),\n/* harmony export */   G2s: () => (/* binding */ G2s)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * Internal helpers for blake hash.\n * @module\n */\n\n/**\n * Internal blake variable.\n * For BLAKE2b, the two extra permutations for rounds 10 and 11 are SIGMA[10..11] = SIGMA[0..1].\n */\n// prettier-ignore\nconst BSIGMA = /* @__PURE__ */ Uint8Array.from([\n    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n    14, 10, 4, 8, 9, 15, 13, 6, 1, 12, 0, 2, 11, 7, 5, 3,\n    11, 8, 12, 0, 5, 2, 15, 13, 10, 14, 3, 6, 7, 1, 9, 4,\n    7, 9, 3, 1, 13, 12, 11, 14, 2, 6, 5, 10, 4, 0, 15, 8,\n    9, 0, 5, 7, 2, 4, 10, 15, 14, 1, 11, 12, 6, 8, 3, 13,\n    2, 12, 6, 10, 0, 11, 8, 3, 4, 13, 7, 5, 15, 14, 1, 9,\n    12, 5, 1, 15, 14, 13, 4, 10, 0, 7, 6, 3, 9, 2, 8, 11,\n    13, 11, 7, 14, 12, 1, 3, 9, 5, 0, 15, 4, 8, 6, 2, 10,\n    6, 15, 14, 9, 11, 3, 0, 8, 12, 2, 13, 7, 1, 4, 10, 5,\n    10, 2, 8, 4, 7, 6, 1, 5, 15, 11, 9, 14, 3, 12, 13, 0,\n    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n    14, 10, 4, 8, 9, 15, 13, 6, 1, 12, 0, 2, 11, 7, 5, 3,\n    // Blake1, unused in others\n    11, 8, 12, 0, 5, 2, 15, 13, 10, 14, 3, 6, 7, 1, 9, 4,\n    7, 9, 3, 1, 13, 12, 11, 14, 2, 6, 5, 10, 4, 0, 15, 8,\n    9, 0, 5, 7, 2, 4, 10, 15, 14, 1, 11, 12, 6, 8, 3, 13,\n    2, 12, 6, 10, 0, 11, 8, 3, 4, 13, 7, 5, 15, 14, 1, 9,\n]);\n// Mixing function G splitted in two halfs\nfunction G1s(a, b, c, d, x) {\n    a = (a + b + x) | 0;\n    d = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotr)(d ^ a, 16);\n    c = (c + d) | 0;\n    b = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotr)(b ^ c, 12);\n    return { a, b, c, d };\n}\nfunction G2s(a, b, c, d, x) {\n    a = (a + b + x) | 0;\n    d = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotr)(d ^ a, 8);\n    c = (c + d) | 0;\n    b = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotr)(b ^ c, 7);\n    return { a, b, c, d };\n}\n//# sourceMappingURL=_blake.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_blake.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_md.js":
/*!***********************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_md.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chi: () => (/* binding */ Chi),\n/* harmony export */   HashMD: () => (/* binding */ HashMD),\n/* harmony export */   Maj: () => (/* binding */ Maj),\n/* harmony export */   SHA224_IV: () => (/* binding */ SHA224_IV),\n/* harmony export */   SHA256_IV: () => (/* binding */ SHA256_IV),\n/* harmony export */   SHA384_IV: () => (/* binding */ SHA384_IV),\n/* harmony export */   SHA512_IV: () => (/* binding */ SHA512_IV),\n/* harmony export */   setBigUint64: () => (/* binding */ setBigUint64)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nfunction Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nfunction Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nclass HashMD extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(this.buffer);\n    }\n    update(data) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(data);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(data);\n        const { view, buffer, blockLen } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aoutput)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(this.buffer.subarray(pos));\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.destroyed = destroyed;\n        to.finished = finished;\n        to.length = length;\n        to.pos = pos;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n}\n/**\n * Initial SHA-2 state: fractional parts of square roots of first 16 primes 2..53.\n * Check out `test/misc/sha2-gen-iv.js` for recomputation guide.\n */\n/** Initial SHA256 state. Bits 0..32 of frac part of sqrt of primes 2..19 */\nconst SHA256_IV = /* @__PURE__ */ Uint32Array.from([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19,\n]);\n/** Initial SHA224 state. Bits 32..64 of frac part of sqrt of primes 23..53 */\nconst SHA224_IV = /* @__PURE__ */ Uint32Array.from([\n    0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4,\n]);\n/** Initial SHA384 state. Bits 0..64 of frac part of sqrt of primes 23..53 */\nconst SHA384_IV = /* @__PURE__ */ Uint32Array.from([\n    0xcbbb9d5d, 0xc1059ed8, 0x629a292a, 0x367cd507, 0x9159015a, 0x3070dd17, 0x152fecd8, 0xf70e5939,\n    0x67332667, 0xffc00b31, 0x8eb44a87, 0x68581511, 0xdb0c2e0d, 0x64f98fa7, 0x47b5481d, 0xbefa4fa4,\n]);\n/** Initial SHA512 state. Bits 0..64 of frac part of sqrt of primes 2..19 */\nconst SHA512_IV = /* @__PURE__ */ Uint32Array.from([\n    0x6a09e667, 0xf3bcc908, 0xbb67ae85, 0x84caa73b, 0x3c6ef372, 0xfe94f82b, 0xa54ff53a, 0x5f1d36f1,\n    0x510e527f, 0xade682d1, 0x9b05688c, 0x2b3e6c1f, 0x1f83d9ab, 0xfb41bd6b, 0x5be0cd19, 0x137e2179,\n]);\n//# sourceMappingURL=_md.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_md.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_u64.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_u64.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   add3H: () => (/* binding */ add3H),\n/* harmony export */   add3L: () => (/* binding */ add3L),\n/* harmony export */   add4H: () => (/* binding */ add4H),\n/* harmony export */   add4L: () => (/* binding */ add4L),\n/* harmony export */   add5H: () => (/* binding */ add5H),\n/* harmony export */   add5L: () => (/* binding */ add5L),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fromBig: () => (/* binding */ fromBig),\n/* harmony export */   rotlBH: () => (/* binding */ rotlBH),\n/* harmony export */   rotlBL: () => (/* binding */ rotlBL),\n/* harmony export */   rotlSH: () => (/* binding */ rotlSH),\n/* harmony export */   rotlSL: () => (/* binding */ rotlSL),\n/* harmony export */   rotr32H: () => (/* binding */ rotr32H),\n/* harmony export */   rotr32L: () => (/* binding */ rotr32L),\n/* harmony export */   rotrBH: () => (/* binding */ rotrBH),\n/* harmony export */   rotrBL: () => (/* binding */ rotrBL),\n/* harmony export */   rotrSH: () => (/* binding */ rotrSH),\n/* harmony export */   rotrSL: () => (/* binding */ rotrSL),\n/* harmony export */   shrSH: () => (/* binding */ shrSH),\n/* harmony export */   shrSL: () => (/* binding */ shrSL),\n/* harmony export */   split: () => (/* binding */ split),\n/* harmony export */   toBig: () => (/* binding */ toBig)\n/* harmony export */ });\n/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    const len = lst.length;\n    let Ah = new Uint32Array(len);\n    let Al = new Uint32Array(len);\n    for (let i = 0; i < len; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\n\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (u64);\n//# sourceMappingURL=_u64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_u64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/blake2.js":
/*!**************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/blake2.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLAKE2: () => (/* binding */ BLAKE2),\n/* harmony export */   BLAKE2b: () => (/* binding */ BLAKE2b),\n/* harmony export */   BLAKE2s: () => (/* binding */ BLAKE2s),\n/* harmony export */   blake2b: () => (/* binding */ blake2b),\n/* harmony export */   blake2s: () => (/* binding */ blake2s),\n/* harmony export */   compress: () => (/* binding */ compress)\n/* harmony export */ });\n/* harmony import */ var _blake_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./_blake.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_blake.js\");\n/* harmony import */ var _md_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./_md.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_md.js\");\n/* harmony import */ var _u64_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_u64.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_u64.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * blake2b (64-bit) & blake2s (8 to 32-bit) hash functions.\n * b could have been faster, but there is no fast u64 in js, so s is 1.5x faster.\n * @module\n */\n\n\n\n// prettier-ignore\n\n// Same as SHA512_IV, but swapped endianness: LE instead of BE. iv[1] is iv[0], etc.\nconst B2B_IV = /* @__PURE__ */ Uint32Array.from([\n    0xf3bcc908, 0x6a09e667, 0x84caa73b, 0xbb67ae85, 0xfe94f82b, 0x3c6ef372, 0x5f1d36f1, 0xa54ff53a,\n    0xade682d1, 0x510e527f, 0x2b3e6c1f, 0x9b05688c, 0xfb41bd6b, 0x1f83d9ab, 0x137e2179, 0x5be0cd19,\n]);\n// Temporary buffer\nconst BBUF = /* @__PURE__ */ new Uint32Array(32);\n// Mixing function G splitted in two halfs\nfunction G1b(a, b, c, d, msg, x) {\n    // NOTE: V is LE here\n    const Xl = msg[x], Xh = msg[x + 1]; // prettier-ignore\n    let Al = BBUF[2 * a], Ah = BBUF[2 * a + 1]; // prettier-ignore\n    let Bl = BBUF[2 * b], Bh = BBUF[2 * b + 1]; // prettier-ignore\n    let Cl = BBUF[2 * c], Ch = BBUF[2 * c + 1]; // prettier-ignore\n    let Dl = BBUF[2 * d], Dh = BBUF[2 * d + 1]; // prettier-ignore\n    // v[a] = (v[a] + v[b] + x) | 0;\n    let ll = _u64_js__WEBPACK_IMPORTED_MODULE_0__.add3L(Al, Bl, Xl);\n    Ah = _u64_js__WEBPACK_IMPORTED_MODULE_0__.add3H(ll, Ah, Bh, Xh);\n    Al = ll | 0;\n    // v[d] = rotr(v[d] ^ v[a], 32)\n    ({ Dh, Dl } = { Dh: Dh ^ Ah, Dl: Dl ^ Al });\n    ({ Dh, Dl } = { Dh: _u64_js__WEBPACK_IMPORTED_MODULE_0__.rotr32H(Dh, Dl), Dl: _u64_js__WEBPACK_IMPORTED_MODULE_0__.rotr32L(Dh, Dl) });\n    // v[c] = (v[c] + v[d]) | 0;\n    ({ h: Ch, l: Cl } = _u64_js__WEBPACK_IMPORTED_MODULE_0__.add(Ch, Cl, Dh, Dl));\n    // v[b] = rotr(v[b] ^ v[c], 24)\n    ({ Bh, Bl } = { Bh: Bh ^ Ch, Bl: Bl ^ Cl });\n    ({ Bh, Bl } = { Bh: _u64_js__WEBPACK_IMPORTED_MODULE_0__.rotrSH(Bh, Bl, 24), Bl: _u64_js__WEBPACK_IMPORTED_MODULE_0__.rotrSL(Bh, Bl, 24) });\n    (BBUF[2 * a] = Al), (BBUF[2 * a + 1] = Ah);\n    (BBUF[2 * b] = Bl), (BBUF[2 * b + 1] = Bh);\n    (BBUF[2 * c] = Cl), (BBUF[2 * c + 1] = Ch);\n    (BBUF[2 * d] = Dl), (BBUF[2 * d + 1] = Dh);\n}\nfunction G2b(a, b, c, d, msg, x) {\n    // NOTE: V is LE here\n    const Xl = msg[x], Xh = msg[x + 1]; // prettier-ignore\n    let Al = BBUF[2 * a], Ah = BBUF[2 * a + 1]; // prettier-ignore\n    let Bl = BBUF[2 * b], Bh = BBUF[2 * b + 1]; // prettier-ignore\n    let Cl = BBUF[2 * c], Ch = BBUF[2 * c + 1]; // prettier-ignore\n    let Dl = BBUF[2 * d], Dh = BBUF[2 * d + 1]; // prettier-ignore\n    // v[a] = (v[a] + v[b] + x) | 0;\n    let ll = _u64_js__WEBPACK_IMPORTED_MODULE_0__.add3L(Al, Bl, Xl);\n    Ah = _u64_js__WEBPACK_IMPORTED_MODULE_0__.add3H(ll, Ah, Bh, Xh);\n    Al = ll | 0;\n    // v[d] = rotr(v[d] ^ v[a], 16)\n    ({ Dh, Dl } = { Dh: Dh ^ Ah, Dl: Dl ^ Al });\n    ({ Dh, Dl } = { Dh: _u64_js__WEBPACK_IMPORTED_MODULE_0__.rotrSH(Dh, Dl, 16), Dl: _u64_js__WEBPACK_IMPORTED_MODULE_0__.rotrSL(Dh, Dl, 16) });\n    // v[c] = (v[c] + v[d]) | 0;\n    ({ h: Ch, l: Cl } = _u64_js__WEBPACK_IMPORTED_MODULE_0__.add(Ch, Cl, Dh, Dl));\n    // v[b] = rotr(v[b] ^ v[c], 63)\n    ({ Bh, Bl } = { Bh: Bh ^ Ch, Bl: Bl ^ Cl });\n    ({ Bh, Bl } = { Bh: _u64_js__WEBPACK_IMPORTED_MODULE_0__.rotrBH(Bh, Bl, 63), Bl: _u64_js__WEBPACK_IMPORTED_MODULE_0__.rotrBL(Bh, Bl, 63) });\n    (BBUF[2 * a] = Al), (BBUF[2 * a + 1] = Ah);\n    (BBUF[2 * b] = Bl), (BBUF[2 * b + 1] = Bh);\n    (BBUF[2 * c] = Cl), (BBUF[2 * c + 1] = Ch);\n    (BBUF[2 * d] = Dl), (BBUF[2 * d + 1] = Dh);\n}\nfunction checkBlake2Opts(outputLen, opts = {}, keyLen, saltLen, persLen) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.anumber)(keyLen);\n    if (outputLen < 0 || outputLen > keyLen)\n        throw new Error('outputLen bigger than keyLen');\n    const { key, salt, personalization } = opts;\n    if (key !== undefined && (key.length < 1 || key.length > keyLen))\n        throw new Error('key length must be undefined or 1..' + keyLen);\n    if (salt !== undefined && salt.length !== saltLen)\n        throw new Error('salt must be undefined or ' + saltLen);\n    if (personalization !== undefined && personalization.length !== persLen)\n        throw new Error('personalization must be undefined or ' + persLen);\n}\n/** Class, from which others are subclassed. */\nclass BLAKE2 extends _utils_js__WEBPACK_IMPORTED_MODULE_1__.Hash {\n    constructor(blockLen, outputLen) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        this.length = 0;\n        this.pos = 0;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.anumber)(blockLen);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.anumber)(outputLen);\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.buffer = new Uint8Array(blockLen);\n        this.buffer32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(this.buffer);\n    }\n    update(data) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(data);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(data);\n        // Main difference with other hashes: there is flag for last block,\n        // so we cannot process current block before we know that there\n        // is the next one. This significantly complicates logic and reduces ability\n        // to do zero-copy processing\n        const { blockLen, buffer, buffer32 } = this;\n        const len = data.length;\n        const offset = data.byteOffset;\n        const buf = data.buffer;\n        for (let pos = 0; pos < len;) {\n            // If buffer is full and we still have input (don't process last block, same as blake2s)\n            if (this.pos === blockLen) {\n                (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(buffer32);\n                this.compress(buffer32, 0, false);\n                (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(buffer32);\n                this.pos = 0;\n            }\n            const take = Math.min(blockLen - this.pos, len - pos);\n            const dataOffset = offset + pos;\n            // full block && aligned to 4 bytes && not last in input\n            if (take === blockLen && !(dataOffset % 4) && pos + take < len) {\n                const data32 = new Uint32Array(buf, dataOffset, Math.floor((len - pos) / 4));\n                (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(data32);\n                for (let pos32 = 0; pos + blockLen < len; pos32 += buffer32.length, pos += blockLen) {\n                    this.length += blockLen;\n                    this.compress(data32, pos32, false);\n                }\n                (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(data32);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            this.length += take;\n            pos += take;\n        }\n        return this;\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aoutput)(out, this);\n        const { pos, buffer32 } = this;\n        this.finished = true;\n        // Padding\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer.subarray(pos));\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(buffer32);\n        this.compress(buffer32, 0, true);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(buffer32);\n        const out32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(out);\n        this.get().forEach((v, i) => (out32[i] = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(v)));\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        const { buffer, length, finished, destroyed, outputLen, pos } = this;\n        to || (to = new this.constructor({ dkLen: outputLen }));\n        to.set(...this.get());\n        to.buffer.set(buffer);\n        to.destroyed = destroyed;\n        to.finished = finished;\n        to.length = length;\n        to.pos = pos;\n        // @ts-ignore\n        to.outputLen = outputLen;\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n}\nclass BLAKE2b extends BLAKE2 {\n    constructor(opts = {}) {\n        const olen = opts.dkLen === undefined ? 64 : opts.dkLen;\n        super(128, olen);\n        // Same as SHA-512, but LE\n        this.v0l = B2B_IV[0] | 0;\n        this.v0h = B2B_IV[1] | 0;\n        this.v1l = B2B_IV[2] | 0;\n        this.v1h = B2B_IV[3] | 0;\n        this.v2l = B2B_IV[4] | 0;\n        this.v2h = B2B_IV[5] | 0;\n        this.v3l = B2B_IV[6] | 0;\n        this.v3h = B2B_IV[7] | 0;\n        this.v4l = B2B_IV[8] | 0;\n        this.v4h = B2B_IV[9] | 0;\n        this.v5l = B2B_IV[10] | 0;\n        this.v5h = B2B_IV[11] | 0;\n        this.v6l = B2B_IV[12] | 0;\n        this.v6h = B2B_IV[13] | 0;\n        this.v7l = B2B_IV[14] | 0;\n        this.v7h = B2B_IV[15] | 0;\n        checkBlake2Opts(olen, opts, 64, 16, 16);\n        let { key, personalization, salt } = opts;\n        let keyLength = 0;\n        if (key !== undefined) {\n            key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(key);\n            keyLength = key.length;\n        }\n        this.v0l ^= this.outputLen | (keyLength << 8) | (0x01 << 16) | (0x01 << 24);\n        if (salt !== undefined) {\n            salt = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(salt);\n            const slt = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(salt);\n            this.v4l ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(slt[0]);\n            this.v4h ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(slt[1]);\n            this.v5l ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(slt[2]);\n            this.v5h ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(slt[3]);\n        }\n        if (personalization !== undefined) {\n            personalization = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(personalization);\n            const pers = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(personalization);\n            this.v6l ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(pers[0]);\n            this.v6h ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(pers[1]);\n            this.v7l ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(pers[2]);\n            this.v7h ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(pers[3]);\n        }\n        if (key !== undefined) {\n            // Pad to blockLen and update\n            const tmp = new Uint8Array(this.blockLen);\n            tmp.set(key);\n            this.update(tmp);\n        }\n    }\n    // prettier-ignore\n    get() {\n        let { v0l, v0h, v1l, v1h, v2l, v2h, v3l, v3h, v4l, v4h, v5l, v5h, v6l, v6h, v7l, v7h } = this;\n        return [v0l, v0h, v1l, v1h, v2l, v2h, v3l, v3h, v4l, v4h, v5l, v5h, v6l, v6h, v7l, v7h];\n    }\n    // prettier-ignore\n    set(v0l, v0h, v1l, v1h, v2l, v2h, v3l, v3h, v4l, v4h, v5l, v5h, v6l, v6h, v7l, v7h) {\n        this.v0l = v0l | 0;\n        this.v0h = v0h | 0;\n        this.v1l = v1l | 0;\n        this.v1h = v1h | 0;\n        this.v2l = v2l | 0;\n        this.v2h = v2h | 0;\n        this.v3l = v3l | 0;\n        this.v3h = v3h | 0;\n        this.v4l = v4l | 0;\n        this.v4h = v4h | 0;\n        this.v5l = v5l | 0;\n        this.v5h = v5h | 0;\n        this.v6l = v6l | 0;\n        this.v6h = v6h | 0;\n        this.v7l = v7l | 0;\n        this.v7h = v7h | 0;\n    }\n    compress(msg, offset, isLast) {\n        this.get().forEach((v, i) => (BBUF[i] = v)); // First half from state.\n        BBUF.set(B2B_IV, 16); // Second half from IV.\n        let { h, l } = _u64_js__WEBPACK_IMPORTED_MODULE_0__.fromBig(BigInt(this.length));\n        BBUF[24] = B2B_IV[8] ^ l; // Low word of the offset.\n        BBUF[25] = B2B_IV[9] ^ h; // High word.\n        // Invert all bits for last block\n        if (isLast) {\n            BBUF[28] = ~BBUF[28];\n            BBUF[29] = ~BBUF[29];\n        }\n        let j = 0;\n        const s = _blake_js__WEBPACK_IMPORTED_MODULE_2__.BSIGMA;\n        for (let i = 0; i < 12; i++) {\n            G1b(0, 4, 8, 12, msg, offset + 2 * s[j++]);\n            G2b(0, 4, 8, 12, msg, offset + 2 * s[j++]);\n            G1b(1, 5, 9, 13, msg, offset + 2 * s[j++]);\n            G2b(1, 5, 9, 13, msg, offset + 2 * s[j++]);\n            G1b(2, 6, 10, 14, msg, offset + 2 * s[j++]);\n            G2b(2, 6, 10, 14, msg, offset + 2 * s[j++]);\n            G1b(3, 7, 11, 15, msg, offset + 2 * s[j++]);\n            G2b(3, 7, 11, 15, msg, offset + 2 * s[j++]);\n            G1b(0, 5, 10, 15, msg, offset + 2 * s[j++]);\n            G2b(0, 5, 10, 15, msg, offset + 2 * s[j++]);\n            G1b(1, 6, 11, 12, msg, offset + 2 * s[j++]);\n            G2b(1, 6, 11, 12, msg, offset + 2 * s[j++]);\n            G1b(2, 7, 8, 13, msg, offset + 2 * s[j++]);\n            G2b(2, 7, 8, 13, msg, offset + 2 * s[j++]);\n            G1b(3, 4, 9, 14, msg, offset + 2 * s[j++]);\n            G2b(3, 4, 9, 14, msg, offset + 2 * s[j++]);\n        }\n        this.v0l ^= BBUF[0] ^ BBUF[16];\n        this.v0h ^= BBUF[1] ^ BBUF[17];\n        this.v1l ^= BBUF[2] ^ BBUF[18];\n        this.v1h ^= BBUF[3] ^ BBUF[19];\n        this.v2l ^= BBUF[4] ^ BBUF[20];\n        this.v2h ^= BBUF[5] ^ BBUF[21];\n        this.v3l ^= BBUF[6] ^ BBUF[22];\n        this.v3h ^= BBUF[7] ^ BBUF[23];\n        this.v4l ^= BBUF[8] ^ BBUF[24];\n        this.v4h ^= BBUF[9] ^ BBUF[25];\n        this.v5l ^= BBUF[10] ^ BBUF[26];\n        this.v5h ^= BBUF[11] ^ BBUF[27];\n        this.v6l ^= BBUF[12] ^ BBUF[28];\n        this.v6h ^= BBUF[13] ^ BBUF[29];\n        this.v7l ^= BBUF[14] ^ BBUF[30];\n        this.v7h ^= BBUF[15] ^ BBUF[31];\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(BBUF);\n    }\n    destroy() {\n        this.destroyed = true;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer32);\n        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n    }\n}\n/**\n * Blake2b hash function. 64-bit. 1.5x slower than blake2s in JS.\n * @param msg - message that would be hashed\n * @param opts - dkLen output length, key for MAC mode, salt, personalization\n */\nconst blake2b = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createOptHasher)((opts) => new BLAKE2b(opts));\n// prettier-ignore\nfunction compress(s, offset, msg, rounds, v0, v1, v2, v3, v4, v5, v6, v7, v8, v9, v10, v11, v12, v13, v14, v15) {\n    let j = 0;\n    for (let i = 0; i < rounds; i++) {\n        ({ a: v0, b: v4, c: v8, d: v12 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G1s)(v0, v4, v8, v12, msg[offset + s[j++]]));\n        ({ a: v0, b: v4, c: v8, d: v12 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G2s)(v0, v4, v8, v12, msg[offset + s[j++]]));\n        ({ a: v1, b: v5, c: v9, d: v13 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G1s)(v1, v5, v9, v13, msg[offset + s[j++]]));\n        ({ a: v1, b: v5, c: v9, d: v13 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G2s)(v1, v5, v9, v13, msg[offset + s[j++]]));\n        ({ a: v2, b: v6, c: v10, d: v14 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G1s)(v2, v6, v10, v14, msg[offset + s[j++]]));\n        ({ a: v2, b: v6, c: v10, d: v14 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G2s)(v2, v6, v10, v14, msg[offset + s[j++]]));\n        ({ a: v3, b: v7, c: v11, d: v15 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G1s)(v3, v7, v11, v15, msg[offset + s[j++]]));\n        ({ a: v3, b: v7, c: v11, d: v15 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G2s)(v3, v7, v11, v15, msg[offset + s[j++]]));\n        ({ a: v0, b: v5, c: v10, d: v15 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G1s)(v0, v5, v10, v15, msg[offset + s[j++]]));\n        ({ a: v0, b: v5, c: v10, d: v15 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G2s)(v0, v5, v10, v15, msg[offset + s[j++]]));\n        ({ a: v1, b: v6, c: v11, d: v12 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G1s)(v1, v6, v11, v12, msg[offset + s[j++]]));\n        ({ a: v1, b: v6, c: v11, d: v12 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G2s)(v1, v6, v11, v12, msg[offset + s[j++]]));\n        ({ a: v2, b: v7, c: v8, d: v13 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G1s)(v2, v7, v8, v13, msg[offset + s[j++]]));\n        ({ a: v2, b: v7, c: v8, d: v13 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G2s)(v2, v7, v8, v13, msg[offset + s[j++]]));\n        ({ a: v3, b: v4, c: v9, d: v14 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G1s)(v3, v4, v9, v14, msg[offset + s[j++]]));\n        ({ a: v3, b: v4, c: v9, d: v14 } = (0,_blake_js__WEBPACK_IMPORTED_MODULE_2__.G2s)(v3, v4, v9, v14, msg[offset + s[j++]]));\n    }\n    return { v0, v1, v2, v3, v4, v5, v6, v7, v8, v9, v10, v11, v12, v13, v14, v15 };\n}\nconst B2S_IV = _md_js__WEBPACK_IMPORTED_MODULE_3__.SHA256_IV;\nclass BLAKE2s extends BLAKE2 {\n    constructor(opts = {}) {\n        const olen = opts.dkLen === undefined ? 32 : opts.dkLen;\n        super(64, olen);\n        // Internal state, same as SHA-256\n        this.v0 = B2S_IV[0] | 0;\n        this.v1 = B2S_IV[1] | 0;\n        this.v2 = B2S_IV[2] | 0;\n        this.v3 = B2S_IV[3] | 0;\n        this.v4 = B2S_IV[4] | 0;\n        this.v5 = B2S_IV[5] | 0;\n        this.v6 = B2S_IV[6] | 0;\n        this.v7 = B2S_IV[7] | 0;\n        checkBlake2Opts(olen, opts, 32, 8, 8);\n        let { key, personalization, salt } = opts;\n        let keyLength = 0;\n        if (key !== undefined) {\n            key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(key);\n            keyLength = key.length;\n        }\n        this.v0 ^= this.outputLen | (keyLength << 8) | (0x01 << 16) | (0x01 << 24);\n        if (salt !== undefined) {\n            salt = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(salt);\n            const slt = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(salt);\n            this.v4 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(slt[0]);\n            this.v5 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(slt[1]);\n        }\n        if (personalization !== undefined) {\n            personalization = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(personalization);\n            const pers = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(personalization);\n            this.v6 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(pers[0]);\n            this.v7 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap8IfBE)(pers[1]);\n        }\n        if (key !== undefined) {\n            // Pad to blockLen and update\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(key);\n            const tmp = new Uint8Array(this.blockLen);\n            tmp.set(key);\n            this.update(tmp);\n        }\n    }\n    get() {\n        const { v0, v1, v2, v3, v4, v5, v6, v7 } = this;\n        return [v0, v1, v2, v3, v4, v5, v6, v7];\n    }\n    // prettier-ignore\n    set(v0, v1, v2, v3, v4, v5, v6, v7) {\n        this.v0 = v0 | 0;\n        this.v1 = v1 | 0;\n        this.v2 = v2 | 0;\n        this.v3 = v3 | 0;\n        this.v4 = v4 | 0;\n        this.v5 = v5 | 0;\n        this.v6 = v6 | 0;\n        this.v7 = v7 | 0;\n    }\n    compress(msg, offset, isLast) {\n        const { h, l } = _u64_js__WEBPACK_IMPORTED_MODULE_0__.fromBig(BigInt(this.length));\n        // prettier-ignore\n        const { v0, v1, v2, v3, v4, v5, v6, v7, v8, v9, v10, v11, v12, v13, v14, v15 } = compress(_blake_js__WEBPACK_IMPORTED_MODULE_2__.BSIGMA, offset, msg, 10, this.v0, this.v1, this.v2, this.v3, this.v4, this.v5, this.v6, this.v7, B2S_IV[0], B2S_IV[1], B2S_IV[2], B2S_IV[3], l ^ B2S_IV[4], h ^ B2S_IV[5], isLast ? ~B2S_IV[6] : B2S_IV[6], B2S_IV[7]);\n        this.v0 ^= v0 ^ v8;\n        this.v1 ^= v1 ^ v9;\n        this.v2 ^= v2 ^ v10;\n        this.v3 ^= v3 ^ v11;\n        this.v4 ^= v4 ^ v12;\n        this.v5 ^= v5 ^ v13;\n        this.v6 ^= v6 ^ v14;\n        this.v7 ^= v7 ^ v15;\n    }\n    destroy() {\n        this.destroyed = true;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer32);\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n    }\n}\n/**\n * Blake2s hash function. Focuses on 8-bit to 32-bit platforms. 1.5x faster than blake2b in JS.\n * @param msg - message that would be hashed\n * @param opts - dkLen output length, key for MAC mode, salt, personalization\n */\nconst blake2s = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createOptHasher)((opts) => new BLAKE2s(opts));\n//# sourceMappingURL=blake2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/blake2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/blake2b.js":
/*!***************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/blake2b.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLAKE2b: () => (/* binding */ BLAKE2b),\n/* harmony export */   blake2b: () => (/* binding */ blake2b)\n/* harmony export */ });\n/* harmony import */ var _blake2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blake2.js */ \"(ssr)/./node_modules/@noble/hashes/esm/blake2.js\");\n/**\n * Blake2b hash function. Focuses on 64-bit platforms, but in JS speed different from Blake2s is negligible.\n * @module\n * @deprecated\n */\n\n/** @deprecated Use import from `noble/hashes/blake2` module */\nconst BLAKE2b = _blake2_js__WEBPACK_IMPORTED_MODULE_0__.BLAKE2b;\n/** @deprecated Use import from `noble/hashes/blake2` module */\nconst blake2b = _blake2_js__WEBPACK_IMPORTED_MODULE_0__.blake2b;\n//# sourceMappingURL=blake2b.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vYmxha2UyYi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQzZEO0FBQzdEO0FBQ08sZ0JBQWdCLCtDQUFHO0FBQzFCO0FBQ08sZ0JBQWdCLCtDQUFHO0FBQzFCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcQG5vYmxlXFxoYXNoZXNcXGVzbVxcYmxha2UyYi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEJsYWtlMmIgaGFzaCBmdW5jdGlvbi4gRm9jdXNlcyBvbiA2NC1iaXQgcGxhdGZvcm1zLCBidXQgaW4gSlMgc3BlZWQgZGlmZmVyZW50IGZyb20gQmxha2UycyBpcyBuZWdsaWdpYmxlLlxuICogQG1vZHVsZVxuICogQGRlcHJlY2F0ZWRcbiAqL1xuaW1wb3J0IHsgQkxBS0UyYiBhcyBCMkIsIGJsYWtlMmIgYXMgYjJiIH0gZnJvbSBcIi4vYmxha2UyLmpzXCI7XG4vKiogQGRlcHJlY2F0ZWQgVXNlIGltcG9ydCBmcm9tIGBub2JsZS9oYXNoZXMvYmxha2UyYCBtb2R1bGUgKi9cbmV4cG9ydCBjb25zdCBCTEFLRTJiID0gQjJCO1xuLyoqIEBkZXByZWNhdGVkIFVzZSBpbXBvcnQgZnJvbSBgbm9ibGUvaGFzaGVzL2JsYWtlMmAgbW9kdWxlICovXG5leHBvcnQgY29uc3QgYmxha2UyYiA9IGIyYjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJsYWtlMmIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/blake2b.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js":
/*!******************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/cryptoNode.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crypto: () => (/* binding */ crypto)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/**\n * Internal webcrypto alias.\n * We prefer WebCrypto aka globalThis.crypto, which exists in node.js 16+.\n * Falls back to Node.js built-in crypto for Node.js <=v14.\n * See utils.ts for details.\n * @module\n */\n// @ts-ignore\n\nconst crypto = /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"webcrypto\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n    ? node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto\n    : /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"randomBytes\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n        ? /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n        : undefined;\n//# sourceMappingURL=cryptoNode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vY3J5cHRvTm9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2tDO0FBQzNCLGVBQWUsMk1BQUUsV0FBVywyTUFBRSxpQkFBaUIsME5BQWlCO0FBQ3ZFLE1BQU0sa0RBQVk7QUFDbEIsTUFBTSwyTUFBRSxXQUFXLDJNQUFFLGlCQUFpQiw0TkFBbUI7QUFDekQsVUFBVSwyTUFBRTtBQUNaO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxAbm9ibGVcXGhhc2hlc1xcZXNtXFxjcnlwdG9Ob2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSW50ZXJuYWwgd2ViY3J5cHRvIGFsaWFzLlxuICogV2UgcHJlZmVyIFdlYkNyeXB0byBha2EgZ2xvYmFsVGhpcy5jcnlwdG8sIHdoaWNoIGV4aXN0cyBpbiBub2RlLmpzIDE2Ky5cbiAqIEZhbGxzIGJhY2sgdG8gTm9kZS5qcyBidWlsdC1pbiBjcnlwdG8gZm9yIE5vZGUuanMgPD12MTQuXG4gKiBTZWUgdXRpbHMudHMgZm9yIGRldGFpbHMuXG4gKiBAbW9kdWxlXG4gKi9cbi8vIEB0cy1pZ25vcmVcbmltcG9ydCAqIGFzIG5jIGZyb20gJ25vZGU6Y3J5cHRvJztcbmV4cG9ydCBjb25zdCBjcnlwdG8gPSBuYyAmJiB0eXBlb2YgbmMgPT09ICdvYmplY3QnICYmICd3ZWJjcnlwdG8nIGluIG5jXG4gICAgPyBuYy53ZWJjcnlwdG9cbiAgICA6IG5jICYmIHR5cGVvZiBuYyA9PT0gJ29iamVjdCcgJiYgJ3JhbmRvbUJ5dGVzJyBpbiBuY1xuICAgICAgICA/IG5jXG4gICAgICAgIDogdW5kZWZpbmVkO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3J5cHRvTm9kZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/utils.js":
/*!*************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hash: () => (/* binding */ Hash),\n/* harmony export */   abytes: () => (/* binding */ abytes),\n/* harmony export */   aexists: () => (/* binding */ aexists),\n/* harmony export */   ahash: () => (/* binding */ ahash),\n/* harmony export */   anumber: () => (/* binding */ anumber),\n/* harmony export */   aoutput: () => (/* binding */ aoutput),\n/* harmony export */   asyncLoop: () => (/* binding */ asyncLoop),\n/* harmony export */   byteSwap: () => (/* binding */ byteSwap),\n/* harmony export */   byteSwap32: () => (/* binding */ byteSwap32),\n/* harmony export */   byteSwapIfBE: () => (/* binding */ byteSwapIfBE),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToUtf8: () => (/* binding */ bytesToUtf8),\n/* harmony export */   checkOpts: () => (/* binding */ checkOpts),\n/* harmony export */   clean: () => (/* binding */ clean),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   createOptHasher: () => (/* binding */ createOptHasher),\n/* harmony export */   createView: () => (/* binding */ createView),\n/* harmony export */   createXOFer: () => (/* binding */ createXOFer),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   isBytes: () => (/* binding */ isBytes),\n/* harmony export */   isLE: () => (/* binding */ isLE),\n/* harmony export */   kdfInputToBytes: () => (/* binding */ kdfInputToBytes),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   randomBytes: () => (/* binding */ randomBytes),\n/* harmony export */   rotl: () => (/* binding */ rotl),\n/* harmony export */   rotr: () => (/* binding */ rotr),\n/* harmony export */   swap32IfBE: () => (/* binding */ swap32IfBE),\n/* harmony export */   swap8IfBE: () => (/* binding */ swap8IfBE),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   u32: () => (/* binding */ u32),\n/* harmony export */   u8: () => (/* binding */ u8),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   wrapConstructor: () => (/* binding */ wrapConstructor),\n/* harmony export */   wrapConstructorWithOpts: () => (/* binding */ wrapConstructorWithOpts),\n/* harmony export */   wrapXOFConstructorWithOpts: () => (/* binding */ wrapXOFConstructorWithOpts)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/crypto */ \"(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js\");\n/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\n\n/** Checks if something is Uint8Array. Be careful: nodejs Buffer will return true. */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is positive integer. */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.createHasher');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\n/** Cast u8 / u16 / u32 to u8. */\nfunction u8(arr) {\n    return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** Cast u8 / u16 / u32 to u32. */\nfunction u32(arr) {\n    return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nfunction clean(...arrays) {\n    for (let i = 0; i < arrays.length; i++) {\n        arrays[i].fill(0);\n    }\n}\n/** Create DataView of an array for easy byte-level manipulation. */\nfunction createView(arr) {\n    return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nfunction rotr(word, shift) {\n    return (word << (32 - shift)) | (word >>> shift);\n}\n/** The rotate left (circular left shift) operation for uint32 */\nfunction rotl(word, shift) {\n    return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nconst isLE = /* @__PURE__ */ (() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n/** The byte swap operation for uint32 */\nfunction byteSwap(word) {\n    return (((word << 24) & 0xff000000) |\n        ((word << 8) & 0xff0000) |\n        ((word >>> 8) & 0xff00) |\n        ((word >>> 24) & 0xff));\n}\n/** Conditionally byte swap if on a big-endian platform */\nconst swap8IfBE = isLE\n    ? (n) => n\n    : (n) => byteSwap(n);\n/** @deprecated */\nconst byteSwapIfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nfunction byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n    return arr;\n}\nconst swap32IfBE = isLE\n    ? (u) => u\n    : byteSwap32;\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = /* @__PURE__ */ (() => \n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return bytes.toHex();\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return Uint8Array.fromHex(hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nconst nextTick = async () => { };\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nfunction bytesToUtf8(bytes) {\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nfunction kdfInputToBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/** Copies several Uint8Arrays into one. */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n        throw new Error('options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n/** For runtime check if class implements interface */\nclass Hash {\n}\n/** Wraps hash function, creating an interface on top of it */\nfunction createHasher(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nfunction createOptHasher(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nfunction createXOFer(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nconst wrapConstructor = createHasher;\nconst wrapConstructorWithOpts = createOptHasher;\nconst wrapXOFConstructorWithOpts = createXOFer;\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nfunction randomBytes(bytesLength = 32) {\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues === 'function') {\n        return _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    // Legacy Node.js compatibility\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.randomBytes === 'function') {\n        return Uint8Array.from(_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.randomBytes(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/utils.js\n");

/***/ })

};
;