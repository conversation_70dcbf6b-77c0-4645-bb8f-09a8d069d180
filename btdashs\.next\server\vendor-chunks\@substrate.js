"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@substrate";
exports.ids = ["vendor-chunks/@substrate"];
exports.modules = {

/***/ "(ssr)/./node_modules/@substrate/ss58-registry/esm/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@substrate/ss58-registry/esm/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Copyright (C) 2021-2024 Parity Technologies (UK) Ltd.\n// SPDX-License-Identifier: Apache-2.0\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n// \thttp://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n\t{\n\t\t\"prefix\": 0,\n\t\t\"network\": \"polkadot\",\n\t\t\"displayName\": \"Polkadot Relay Chain\",\n\t\t\"symbols\": [\n\t\t\t\"DOT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t10\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://polkadot.network\"\n\t},\n\t{\n\t\t\"prefix\": 1,\n\t\t\"network\": \"BareSr25519\",\n\t\t\"displayName\": \"Bare 32-bit Schnorr/Ristretto (S/R 25519) public key.\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": \"Sr25519\",\n\t\t\"website\": null\n\t},\n\t{\n\t\t\"prefix\": 2,\n\t\t\"network\": \"kusama\",\n\t\t\"displayName\": \"Kusama Relay Chain\",\n\t\t\"symbols\": [\n\t\t\t\"KSM\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://kusama.network\"\n\t},\n\t{\n\t\t\"prefix\": 3,\n\t\t\"network\": \"BareEd25519\",\n\t\t\"displayName\": \"Bare 32-bit Ed25519 public key.\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": \"Ed25519\",\n\t\t\"website\": null\n\t},\n\t{\n\t\t\"prefix\": 4,\n\t\t\"network\": \"katalchain\",\n\t\t\"displayName\": \"Katal Chain\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": null\n\t},\n\t{\n\t\t\"prefix\": 5,\n\t\t\"network\": \"astar\",\n\t\t\"displayName\": \"Astar Network\",\n\t\t\"symbols\": [\n\t\t\t\"ASTR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://astar.network\"\n\t},\n\t{\n\t\t\"prefix\": 6,\n\t\t\"network\": \"bifrost\",\n\t\t\"displayName\": \"Bifrost\",\n\t\t\"symbols\": [\n\t\t\t\"BNC\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://bifrost.finance/\"\n\t},\n\t{\n\t\t\"prefix\": 7,\n\t\t\"network\": \"edgeware\",\n\t\t\"displayName\": \"Edgeware\",\n\t\t\"symbols\": [\n\t\t\t\"EDG\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://edgewa.re\"\n\t},\n\t{\n\t\t\"prefix\": 8,\n\t\t\"network\": \"karura\",\n\t\t\"displayName\": \"Karura\",\n\t\t\"symbols\": [\n\t\t\t\"KAR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://karura.network/\"\n\t},\n\t{\n\t\t\"prefix\": 9,\n\t\t\"network\": \"reynolds\",\n\t\t\"displayName\": \"Laminar Reynolds Canary\",\n\t\t\"symbols\": [\n\t\t\t\"REY\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"http://laminar.network/\"\n\t},\n\t{\n\t\t\"prefix\": 10,\n\t\t\"network\": \"acala\",\n\t\t\"displayName\": \"Acala\",\n\t\t\"symbols\": [\n\t\t\t\"ACA\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://acala.network/\"\n\t},\n\t{\n\t\t\"prefix\": 11,\n\t\t\"network\": \"laminar\",\n\t\t\"displayName\": \"Laminar\",\n\t\t\"symbols\": [\n\t\t\t\"LAMI\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"http://laminar.network/\"\n\t},\n\t{\n\t\t\"prefix\": 12,\n\t\t\"network\": \"polymesh\",\n\t\t\"displayName\": \"Polymesh\",\n\t\t\"symbols\": [\n\t\t\t\"POLYX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t6\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://polymath.network/\"\n\t},\n\t{\n\t\t\"prefix\": 13,\n\t\t\"network\": \"integritee\",\n\t\t\"displayName\": \"Integritee\",\n\t\t\"symbols\": [\n\t\t\t\"TEER\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://integritee.network\"\n\t},\n\t{\n\t\t\"prefix\": 14,\n\t\t\"network\": \"totem\",\n\t\t\"displayName\": \"Totem\",\n\t\t\"symbols\": [\n\t\t\t\"TOTEM\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t0\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://totemaccounting.com\"\n\t},\n\t{\n\t\t\"prefix\": 15,\n\t\t\"network\": \"synesthesia\",\n\t\t\"displayName\": \"Synesthesia\",\n\t\t\"symbols\": [\n\t\t\t\"SYN\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://synesthesia.network/\"\n\t},\n\t{\n\t\t\"prefix\": 16,\n\t\t\"network\": \"kulupu\",\n\t\t\"displayName\": \"Kulupu\",\n\t\t\"symbols\": [\n\t\t\t\"KLP\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://kulupu.network/\"\n\t},\n\t{\n\t\t\"prefix\": 17,\n\t\t\"network\": \"dark\",\n\t\t\"displayName\": \"Dark Mainnet\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": null\n\t},\n\t{\n\t\t\"prefix\": 18,\n\t\t\"network\": \"darwinia\",\n\t\t\"displayName\": \"Darwinia Network\",\n\t\t\"symbols\": [\n\t\t\t\"RING\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"secp256k1\",\n\t\t\"website\": \"https://darwinia.network\"\n\t},\n\t{\n\t\t\"prefix\": 19,\n\t\t\"network\": \"watr\",\n\t\t\"displayName\": \"Watr Protocol\",\n\t\t\"symbols\": [\n\t\t\t\"WATR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://www.watr.org\"\n\t},\n\t{\n\t\t\"prefix\": 20,\n\t\t\"network\": \"stafi\",\n\t\t\"displayName\": \"Stafi\",\n\t\t\"symbols\": [\n\t\t\t\"FIS\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://stafi.io\"\n\t},\n\t{\n\t\t\"prefix\": 21,\n\t\t\"network\": \"karmachain\",\n\t\t\"displayName\": \"Karmacoin\",\n\t\t\"symbols\": [\n\t\t\t\"KCOIN\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t6\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://karmaco.in\"\n\t},\n\t{\n\t\t\"prefix\": 22,\n\t\t\"network\": \"dock-pos-mainnet\",\n\t\t\"displayName\": \"Dock Mainnet\",\n\t\t\"symbols\": [\n\t\t\t\"DCK\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t6\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://dock.io\"\n\t},\n\t{\n\t\t\"prefix\": 23,\n\t\t\"network\": \"shift\",\n\t\t\"displayName\": \"ShiftNrg\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": null\n\t},\n\t{\n\t\t\"prefix\": 24,\n\t\t\"network\": \"zero\",\n\t\t\"displayName\": \"ZERO\",\n\t\t\"symbols\": [\n\t\t\t\"ZERO\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://zero.io\"\n\t},\n\t{\n\t\t\"prefix\": 25,\n\t\t\"network\": \"zero-alphaville\",\n\t\t\"displayName\": \"ZERO Alphaville\",\n\t\t\"symbols\": [\n\t\t\t\"ZERO\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://zero.io\"\n\t},\n\t{\n\t\t\"prefix\": 26,\n\t\t\"network\": \"jupiter\",\n\t\t\"displayName\": \"Jupiter\",\n\t\t\"symbols\": [\n\t\t\t\"jDOT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t10\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://jupiter.patract.io\"\n\t},\n\t{\n\t\t\"prefix\": 27,\n\t\t\"network\": \"kabocha\",\n\t\t\"displayName\": \"Kabocha\",\n\t\t\"symbols\": [\n\t\t\t\"KAB\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://kabocha.network\"\n\t},\n\t{\n\t\t\"prefix\": 28,\n\t\t\"network\": \"subsocial\",\n\t\t\"displayName\": \"Subsocial\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": null\n\t},\n\t{\n\t\t\"prefix\": 29,\n\t\t\"network\": \"cord\",\n\t\t\"displayName\": \"CORD Network\",\n\t\t\"symbols\": [\n\t\t\t\"DHI\",\n\t\t\t\"WAY\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12,\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://cord.network/\"\n\t},\n\t{\n\t\t\"prefix\": 30,\n\t\t\"network\": \"phala\",\n\t\t\"displayName\": \"Phala Network\",\n\t\t\"symbols\": [\n\t\t\t\"PHA\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://phala.network\"\n\t},\n\t{\n\t\t\"prefix\": 31,\n\t\t\"network\": \"litentry\",\n\t\t\"displayName\": \"Litentry Network\",\n\t\t\"symbols\": [\n\t\t\t\"LIT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://litentry.com/\"\n\t},\n\t{\n\t\t\"prefix\": 32,\n\t\t\"network\": \"robonomics\",\n\t\t\"displayName\": \"Robonomics\",\n\t\t\"symbols\": [\n\t\t\t\"XRT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t9\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://robonomics.network\"\n\t},\n\t{\n\t\t\"prefix\": 33,\n\t\t\"network\": \"datahighway\",\n\t\t\"displayName\": \"DataHighway\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": null\n\t},\n\t{\n\t\t\"prefix\": 34,\n\t\t\"network\": \"ares\",\n\t\t\"displayName\": \"Ares Protocol\",\n\t\t\"symbols\": [\n\t\t\t\"ARES\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://www.aresprotocol.com/\"\n\t},\n\t{\n\t\t\"prefix\": 35,\n\t\t\"network\": \"vln\",\n\t\t\"displayName\": \"Valiu Liquidity Network\",\n\t\t\"symbols\": [\n\t\t\t\"USDv\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t15\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://valiu.com/\"\n\t},\n\t{\n\t\t\"prefix\": 36,\n\t\t\"network\": \"centrifuge\",\n\t\t\"displayName\": \"Centrifuge Chain\",\n\t\t\"symbols\": [\n\t\t\t\"CFG\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://centrifuge.io/\"\n\t},\n\t{\n\t\t\"prefix\": 37,\n\t\t\"network\": \"nodle\",\n\t\t\"displayName\": \"Nodle Chain\",\n\t\t\"symbols\": [\n\t\t\t\"NODL\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t11\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://nodle.io/\"\n\t},\n\t{\n\t\t\"prefix\": 38,\n\t\t\"network\": \"kilt\",\n\t\t\"displayName\": \"KILT Spiritnet\",\n\t\t\"symbols\": [\n\t\t\t\"KILT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t15\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://kilt.io/\"\n\t},\n\t{\n\t\t\"prefix\": 39,\n\t\t\"network\": \"mathchain\",\n\t\t\"displayName\": \"MathChain mainnet\",\n\t\t\"symbols\": [\n\t\t\t\"MATH\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://mathwallet.org\"\n\t},\n\t{\n\t\t\"prefix\": 40,\n\t\t\"network\": \"mathchain-testnet\",\n\t\t\"displayName\": \"MathChain testnet\",\n\t\t\"symbols\": [\n\t\t\t\"MATH\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://mathwallet.org\"\n\t},\n\t{\n\t\t\"prefix\": 41,\n\t\t\"network\": \"polimec\",\n\t\t\"displayName\": \"Polimec Protocol\",\n\t\t\"symbols\": [\n\t\t\t\"PLMC\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t10\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://www.polimec.org/\"\n\t},\n\t{\n\t\t\"prefix\": 42,\n\t\t\"network\": \"substrate\",\n\t\t\"displayName\": \"Substrate\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://substrate.io/\"\n\t},\n\t{\n\t\t\"prefix\": 43,\n\t\t\"network\": \"BareSecp256k1\",\n\t\t\"displayName\": \"Bare 32-bit ECDSA SECP-256k1 public key.\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": \"secp256k1\",\n\t\t\"website\": null\n\t},\n\t{\n\t\t\"prefix\": 44,\n\t\t\"network\": \"chainx\",\n\t\t\"displayName\": \"ChainX\",\n\t\t\"symbols\": [\n\t\t\t\"PCX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t8\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://chainx.org/\"\n\t},\n\t{\n\t\t\"prefix\": 45,\n\t\t\"network\": \"uniarts\",\n\t\t\"displayName\": \"UniArts Network\",\n\t\t\"symbols\": [\n\t\t\t\"UART\",\n\t\t\t\"UINK\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12,\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://uniarts.me\"\n\t},\n\t{\n\t\t\"prefix\": 46,\n\t\t\"network\": \"reserved46\",\n\t\t\"displayName\": \"This prefix is reserved.\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": null,\n\t\t\"website\": null\n\t},\n\t{\n\t\t\"prefix\": 47,\n\t\t\"network\": \"reserved47\",\n\t\t\"displayName\": \"This prefix is reserved.\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": null,\n\t\t\"website\": null\n\t},\n\t{\n\t\t\"prefix\": 48,\n\t\t\"network\": \"neatcoin\",\n\t\t\"displayName\": \"Neatcoin Mainnet\",\n\t\t\"symbols\": [\n\t\t\t\"NEAT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://neatcoin.org\"\n\t},\n\t{\n\t\t\"prefix\": 49,\n\t\t\"network\": \"picasso\",\n\t\t\"displayName\": \"Picasso\",\n\t\t\"symbols\": [\n\t\t\t\"PICA\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://picasso.composable.finance\"\n\t},\n\t{\n\t\t\"prefix\": 50,\n\t\t\"network\": \"composable\",\n\t\t\"displayName\": \"Composable Finance\",\n\t\t\"symbols\": [\n\t\t\t\"LAYR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://composable.finance\"\n\t},\n\t{\n\t\t\"prefix\": 51,\n\t\t\"network\": \"oak\",\n\t\t\"displayName\": \"OAK Network\",\n\t\t\"symbols\": [\n\t\t\t\"OAK\",\n\t\t\t\"TUR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t10,\n\t\t\t10\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://oak.tech\"\n\t},\n\t{\n\t\t\"prefix\": 52,\n\t\t\"network\": \"KICO\",\n\t\t\"displayName\": \"KICO\",\n\t\t\"symbols\": [\n\t\t\t\"KICO\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t14\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://dico.io\"\n\t},\n\t{\n\t\t\"prefix\": 53,\n\t\t\"network\": \"DICO\",\n\t\t\"displayName\": \"DICO\",\n\t\t\"symbols\": [\n\t\t\t\"DICO\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t14\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://dico.io\"\n\t},\n\t{\n\t\t\"prefix\": 54,\n\t\t\"network\": \"cere\",\n\t\t\"displayName\": \"Cere Network\",\n\t\t\"symbols\": [\n\t\t\t\"CERE\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t10\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://cere.network\"\n\t},\n\t{\n\t\t\"prefix\": 55,\n\t\t\"network\": \"xxnetwork\",\n\t\t\"displayName\": \"xx network\",\n\t\t\"symbols\": [\n\t\t\t\"XX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t9\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://xx.network\"\n\t},\n\t{\n\t\t\"prefix\": 56,\n\t\t\"network\": \"pendulum\",\n\t\t\"displayName\": \"Pendulum chain\",\n\t\t\"symbols\": [\n\t\t\t\"PEN\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://pendulumchain.org/\"\n\t},\n\t{\n\t\t\"prefix\": 57,\n\t\t\"network\": \"amplitude\",\n\t\t\"displayName\": \"Amplitude chain\",\n\t\t\"symbols\": [\n\t\t\t\"AMPE\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://pendulumchain.org/\"\n\t},\n\t{\n\t\t\"prefix\": 58,\n\t\t\"network\": \"eternal-civilization\",\n\t\t\"displayName\": \"Eternal Civilization\",\n\t\t\"symbols\": [\n\t\t\t\"ECC\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"http://www.ysknfr.cn/\"\n\t},\n\t{\n\t\t\"prefix\": 63,\n\t\t\"network\": \"hydradx\",\n\t\t\"displayName\": \"Hydration\",\n\t\t\"symbols\": [\n\t\t\t\"HDX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://hydration.net\"\n\t},\n\t{\n\t\t\"prefix\": 65,\n\t\t\"network\": \"aventus\",\n\t\t\"displayName\": \"Aventus Mainnet\",\n\t\t\"symbols\": [\n\t\t\t\"AVT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://aventus.io\"\n\t},\n\t{\n\t\t\"prefix\": 66,\n\t\t\"network\": \"crust\",\n\t\t\"displayName\": \"Crust Network\",\n\t\t\"symbols\": [\n\t\t\t\"CRU\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://crust.network\"\n\t},\n\t{\n\t\t\"prefix\": 67,\n\t\t\"network\": \"genshiro\",\n\t\t\"displayName\": \"Genshiro Network\",\n\t\t\"symbols\": [\n\t\t\t\"GENS\",\n\t\t\t\"EQD\",\n\t\t\t\"LPT0\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t9,\n\t\t\t9,\n\t\t\t9\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://genshiro.equilibrium.io\"\n\t},\n\t{\n\t\t\"prefix\": 68,\n\t\t\"network\": \"equilibrium\",\n\t\t\"displayName\": \"Equilibrium Network\",\n\t\t\"symbols\": [\n\t\t\t\"EQ\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t9\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://equilibrium.io\"\n\t},\n\t{\n\t\t\"prefix\": 69,\n\t\t\"network\": \"sora\",\n\t\t\"displayName\": \"SORA Network\",\n\t\t\"symbols\": [\n\t\t\t\"XOR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://sora.org\"\n\t},\n\t{\n\t\t\"prefix\": 71,\n\t\t\"network\": \"p3d\",\n\t\t\"displayName\": \"3DP network\",\n\t\t\"symbols\": [\n\t\t\t\"P3D\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://3dpass.org\"\n\t},\n\t{\n\t\t\"prefix\": 72,\n\t\t\"network\": \"p3dt\",\n\t\t\"displayName\": \"3DP test network\",\n\t\t\"symbols\": [\n\t\t\t\"P3Dt\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://3dpass.org\"\n\t},\n\t{\n\t\t\"prefix\": 73,\n\t\t\"network\": \"zeitgeist\",\n\t\t\"displayName\": \"Zeitgeist\",\n\t\t\"symbols\": [\n\t\t\t\"ZTG\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t10\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://zeitgeist.pm\"\n\t},\n\t{\n\t\t\"prefix\": 77,\n\t\t\"network\": \"manta\",\n\t\t\"displayName\": \"Manta network\",\n\t\t\"symbols\": [\n\t\t\t\"MANTA\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://manta.network\"\n\t},\n\t{\n\t\t\"prefix\": 78,\n\t\t\"network\": \"calamari\",\n\t\t\"displayName\": \"Calamari: Manta Canary Network\",\n\t\t\"symbols\": [\n\t\t\t\"KMA\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://manta.network\"\n\t},\n\t{\n\t\t\"prefix\": 81,\n\t\t\"network\": \"sora_dot_para\",\n\t\t\"displayName\": \"SORA Polkadot Parachain\",\n\t\t\"symbols\": [\n\t\t\t\"XOR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://sora.org\"\n\t},\n\t{\n\t\t\"prefix\": 88,\n\t\t\"network\": \"polkadex\",\n\t\t\"displayName\": \"Polkadex Mainnet\",\n\t\t\"symbols\": [\n\t\t\t\"PDEX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://polkadex.trade\"\n\t},\n\t{\n\t\t\"prefix\": 89,\n\t\t\"network\": \"polkadexparachain\",\n\t\t\"displayName\": \"Polkadex Parachain\",\n\t\t\"symbols\": [\n\t\t\t\"PDEX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://polkadex.trade\"\n\t},\n\t{\n\t\t\"prefix\": 90,\n\t\t\"network\": \"frequency\",\n\t\t\"displayName\": \"Frequency\",\n\t\t\"symbols\": [\n\t\t\t\"FRQCY\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t8\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://www.frequency.xyz\"\n\t},\n\t{\n\t\t\"prefix\": 92,\n\t\t\"network\": \"anmol\",\n\t\t\"displayName\": \"Anmol Network\",\n\t\t\"symbols\": [\n\t\t\t\"ANML\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://anmol.network/\"\n\t},\n\t{\n\t\t\"prefix\": 93,\n\t\t\"network\": \"fragnova\",\n\t\t\"displayName\": \"Fragnova Network\",\n\t\t\"symbols\": [\n\t\t\t\"NOVA\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://fragnova.com\"\n\t},\n\t{\n\t\t\"prefix\": 98,\n\t\t\"network\": \"polkasmith\",\n\t\t\"displayName\": \"PolkaSmith Canary Network\",\n\t\t\"symbols\": [\n\t\t\t\"PKS\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://polkafoundry.com\"\n\t},\n\t{\n\t\t\"prefix\": 99,\n\t\t\"network\": \"polkafoundry\",\n\t\t\"displayName\": \"PolkaFoundry Network\",\n\t\t\"symbols\": [\n\t\t\t\"PKF\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://polkafoundry.com\"\n\t},\n\t{\n\t\t\"prefix\": 100,\n\t\t\"network\": \"ibtida\",\n\t\t\"displayName\": \"Anmol Network Ibtida Canary network\",\n\t\t\"symbols\": [\n\t\t\t\"IANML\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://anmol.network/\"\n\t},\n\t{\n\t\t\"prefix\": 101,\n\t\t\"network\": \"origintrail-parachain\",\n\t\t\"displayName\": \"OriginTrail Parachain\",\n\t\t\"symbols\": [\n\t\t\t\"OTP\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://parachain.origintrail.io/\"\n\t},\n\t{\n\t\t\"prefix\": 105,\n\t\t\"network\": \"pontem-network\",\n\t\t\"displayName\": \"Pontem Network\",\n\t\t\"symbols\": [\n\t\t\t\"PONT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t10\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://pontem.network\"\n\t},\n\t{\n\t\t\"prefix\": 110,\n\t\t\"network\": \"heiko\",\n\t\t\"displayName\": \"Heiko\",\n\t\t\"symbols\": [\n\t\t\t\"HKO\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://parallel.fi/\"\n\t},\n\t{\n\t\t\"prefix\": 113,\n\t\t\"network\": \"integritee-incognito\",\n\t\t\"displayName\": \"Integritee Incognito\",\n\t\t\"symbols\": [],\n\t\t\"decimals\": [],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://integritee.network\"\n\t},\n\t{\n\t\t\"prefix\": 117,\n\t\t\"network\": \"tinker\",\n\t\t\"displayName\": \"Tinker\",\n\t\t\"symbols\": [\n\t\t\t\"TNKR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://invarch.network\"\n\t},\n\t{\n\t\t\"prefix\": 126,\n\t\t\"network\": \"joystream\",\n\t\t\"displayName\": \"Joystream\",\n\t\t\"symbols\": [\n\t\t\t\"JOY\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t10\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://www.joystream.org\"\n\t},\n\t{\n\t\t\"prefix\": 128,\n\t\t\"network\": \"clover\",\n\t\t\"displayName\": \"Clover Finance\",\n\t\t\"symbols\": [\n\t\t\t\"CLV\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://clover.finance\"\n\t},\n\t{\n\t\t\"prefix\": 129,\n\t\t\"network\": \"dorafactory-polkadot\",\n\t\t\"displayName\": \"Dorafactory Polkadot Network\",\n\t\t\"symbols\": [\n\t\t\t\"DORA\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://dorafactory.org\"\n\t},\n\t{\n\t\t\"prefix\": 131,\n\t\t\"network\": \"litmus\",\n\t\t\"displayName\": \"Litmus Network\",\n\t\t\"symbols\": [\n\t\t\t\"LIT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://litentry.com/\"\n\t},\n\t{\n\t\t\"prefix\": 136,\n\t\t\"network\": \"altair\",\n\t\t\"displayName\": \"Altair\",\n\t\t\"symbols\": [\n\t\t\t\"AIR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://centrifuge.io/\"\n\t},\n\t{\n\t\t\"prefix\": 137,\n\t\t\"network\": \"vara\",\n\t\t\"displayName\": \"Vara Network\",\n\t\t\"symbols\": [\n\t\t\t\"VARA\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://vara.network/\"\n\t},\n\t{\n\t\t\"prefix\": 172,\n\t\t\"network\": \"parallel\",\n\t\t\"displayName\": \"Parallel\",\n\t\t\"symbols\": [\n\t\t\t\"PARA\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://parallel.fi/\"\n\t},\n\t{\n\t\t\"prefix\": 252,\n\t\t\"network\": \"social-network\",\n\t\t\"displayName\": \"Social Network\",\n\t\t\"symbols\": [\n\t\t\t\"NET\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://social.network\"\n\t},\n\t{\n\t\t\"prefix\": 255,\n\t\t\"network\": \"quartz_mainnet\",\n\t\t\"displayName\": \"QUARTZ by UNIQUE\",\n\t\t\"symbols\": [\n\t\t\t\"QTZ\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://unique.network\"\n\t},\n\t{\n\t\t\"prefix\": 268,\n\t\t\"network\": \"pioneer_network\",\n\t\t\"displayName\": \"Pioneer Network by Bit.Country\",\n\t\t\"symbols\": [\n\t\t\t\"NEER\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://bit.country\"\n\t},\n\t{\n\t\t\"prefix\": 420,\n\t\t\"network\": \"sora_kusama_para\",\n\t\t\"displayName\": \"SORA Kusama Parachain\",\n\t\t\"symbols\": [\n\t\t\t\"XOR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://sora.org\"\n\t},\n\t{\n\t\t\"prefix\": 440,\n\t\t\"network\": \"allfeat_network\",\n\t\t\"displayName\": \"Allfeat Network\",\n\t\t\"symbols\": [\n\t\t\t\"AFT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://allfeat.network\"\n\t},\n\t{\n\t\t\"prefix\": 666,\n\t\t\"network\": \"metaquity_network\",\n\t\t\"displayName\": \"Metaquity Network\",\n\t\t\"symbols\": [\n\t\t\t\"MQTY\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://metaquity.xyz/\"\n\t},\n\t{\n\t\t\"prefix\": 777,\n\t\t\"network\": \"curio\",\n\t\t\"displayName\": \"Curio\",\n\t\t\"symbols\": [\n\t\t\t\"CGT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://parachain.capitaldex.exchange/\"\n\t},\n\t{\n\t\t\"prefix\": 789,\n\t\t\"network\": \"geek\",\n\t\t\"displayName\": \"GEEK Network\",\n\t\t\"symbols\": [\n\t\t\t\"GEEK\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://geek.gl\"\n\t},\n\t{\n\t\t\"prefix\": 995,\n\t\t\"network\": \"ternoa\",\n\t\t\"displayName\": \"Ternoa\",\n\t\t\"symbols\": [\n\t\t\t\"CAPS\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://www.ternoa.network\"\n\t},\n\t{\n\t\t\"prefix\": 1110,\n\t\t\"network\": \"efinity\",\n\t\t\"displayName\": \"Efinity\",\n\t\t\"symbols\": [\n\t\t\t\"EFI\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://efinity.io/\"\n\t},\n\t{\n\t\t\"prefix\": 1221,\n\t\t\"network\": \"peaq\",\n\t\t\"displayName\": \"Peaq Network\",\n\t\t\"symbols\": [\n\t\t\t\"PEAQ\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"Sr25519\",\n\t\t\"website\": \"https://www.peaq.network/\"\n\t},\n\t{\n\t\t\"prefix\": 1222,\n\t\t\"network\": \"krest\",\n\t\t\"displayName\": \"Krest Network\",\n\t\t\"symbols\": [\n\t\t\t\"KREST\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"Sr25519\",\n\t\t\"website\": \"https://www.peaq.network/\"\n\t},\n\t{\n\t\t\"prefix\": 1284,\n\t\t\"network\": \"moonbeam\",\n\t\t\"displayName\": \"Moonbeam\",\n\t\t\"symbols\": [\n\t\t\t\"GLMR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"secp256k1\",\n\t\t\"website\": \"https://moonbeam.network\"\n\t},\n\t{\n\t\t\"prefix\": 1285,\n\t\t\"network\": \"moonriver\",\n\t\t\"displayName\": \"Moonriver\",\n\t\t\"symbols\": [\n\t\t\t\"MOVR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"secp256k1\",\n\t\t\"website\": \"https://moonbeam.network\"\n\t},\n\t{\n\t\t\"prefix\": 1328,\n\t\t\"network\": \"ajuna\",\n\t\t\"displayName\": \"Ajuna Network\",\n\t\t\"symbols\": [\n\t\t\t\"AJUN\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://ajuna.io\"\n\t},\n\t{\n\t\t\"prefix\": 1337,\n\t\t\"network\": \"bajun\",\n\t\t\"displayName\": \"Bajun Network\",\n\t\t\"symbols\": [\n\t\t\t\"BAJU\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://ajuna.io\"\n\t},\n\t{\n\t\t\"prefix\": 1516,\n\t\t\"network\": \"societal\",\n\t\t\"displayName\": \"Societal\",\n\t\t\"symbols\": [\n\t\t\t\"SCTL\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://www.sctl.xyz\"\n\t},\n\t{\n\t\t\"prefix\": 1985,\n\t\t\"network\": \"seals\",\n\t\t\"displayName\": \"Seals Network\",\n\t\t\"symbols\": [\n\t\t\t\"SEAL\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t9\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://seals.app\"\n\t},\n\t{\n\t\t\"prefix\": 2007,\n\t\t\"network\": \"kapex\",\n\t\t\"displayName\": \"Kapex\",\n\t\t\"symbols\": [\n\t\t\t\"KAPEX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://totemaccounting.com\"\n\t},\n\t{\n\t\t\"prefix\": 2009,\n\t\t\"network\": \"cloudwalk_mainnet\",\n\t\t\"displayName\": \"CloudWalk Network Mainnet\",\n\t\t\"symbols\": [\n\t\t\t\"CWN\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://explorer.mainnet.cloudwalk.io\"\n\t},\n\t{\n\t\t\"prefix\": 2021,\n\t\t\"network\": \"logion\",\n\t\t\"displayName\": \"logion network\",\n\t\t\"symbols\": [\n\t\t\t\"LGNT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://logion.network\"\n\t},\n\t{\n\t\t\"prefix\": 2024,\n\t\t\"network\": \"vow-chain\",\n\t\t\"displayName\": \"Enigmatic Smile\",\n\t\t\"symbols\": [\n\t\t\t\"VOW\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://www.vow.foundation/\"\n\t},\n\t{\n\t\t\"prefix\": 2032,\n\t\t\"network\": \"interlay\",\n\t\t\"displayName\": \"Interlay\",\n\t\t\"symbols\": [\n\t\t\t\"INTR\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t10\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://interlay.io/\"\n\t},\n\t{\n\t\t\"prefix\": 2092,\n\t\t\"network\": \"kintsugi\",\n\t\t\"displayName\": \"Kintsugi\",\n\t\t\"symbols\": [\n\t\t\t\"KINT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://interlay.io/\"\n\t},\n\t{\n\t\t\"prefix\": 2106,\n\t\t\"network\": \"bitgreen\",\n\t\t\"displayName\": \"Bitgreen\",\n\t\t\"symbols\": [\n\t\t\t\"BBB\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://bitgreen.org/\"\n\t},\n\t{\n\t\t\"prefix\": 2112,\n\t\t\"network\": \"chainflip\",\n\t\t\"displayName\": \"Chainflip\",\n\t\t\"symbols\": [\n\t\t\t\"FLIP\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://chainflip.io/\"\n\t},\n\t{\n\t\t\"prefix\": 2199,\n\t\t\"network\": \"moonsama\",\n\t\t\"displayName\": \"Moonsama\",\n\t\t\"symbols\": [\n\t\t\t\"SAMA\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"secp256k1\",\n\t\t\"website\": \"https://moonsama.com\"\n\t},\n\t{\n\t\t\"prefix\": 2206,\n\t\t\"network\": \"ICE\",\n\t\t\"displayName\": \"ICE Network\",\n\t\t\"symbols\": [\n\t\t\t\"ICY\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://icenetwork.io\"\n\t},\n\t{\n\t\t\"prefix\": 2207,\n\t\t\"network\": \"SNOW\",\n\t\t\"displayName\": \"SNOW: ICE Canary Network\",\n\t\t\"symbols\": [\n\t\t\t\"ICZ\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://icenetwork.io\"\n\t},\n\t{\n\t\t\"prefix\": 2254,\n\t\t\"network\": \"subspace_testnet\",\n\t\t\"displayName\": \"Subspace testnet\",\n\t\t\"symbols\": [\n\t\t\t\"tSSC\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://subspace.network\"\n\t},\n\t{\n\t\t\"prefix\": 3333,\n\t\t\"network\": \"peerplays\",\n\t\t\"displayName\": \"Peerplays\",\n\t\t\"symbols\": [\n\t\t\t\"PPY\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"secp256k1\",\n\t\t\"website\": \"https://www.peerplays.com/\"\n\t},\n\t{\n\t\t\"prefix\": 4450,\n\t\t\"network\": \"g1\",\n\t\t\"displayName\": \"Ğ1\",\n\t\t\"symbols\": [\n\t\t\t\"G1\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t2\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://duniter.org\"\n\t},\n\t{\n\t\t\"prefix\": 5234,\n\t\t\"network\": \"humanode\",\n\t\t\"displayName\": \"Humanode Network\",\n\t\t\"symbols\": [\n\t\t\t\"HMND\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://humanode.io\"\n\t},\n\t{\n\t\t\"prefix\": 5845,\n\t\t\"network\": \"tangle\",\n\t\t\"displayName\": \"Tangle Network\",\n\t\t\"symbols\": [\n\t\t\t\"TNT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://www.tangle.tools/\"\n\t},\n\t{\n\t\t\"prefix\": 6094,\n\t\t\"network\": \"autonomys\",\n\t\t\"displayName\": \"Autonomys\",\n\t\t\"symbols\": [\n\t\t\t\"AI3\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://autonomys.xyz\"\n\t},\n\t{\n\t\t\"prefix\": 7007,\n\t\t\"network\": \"tidefi\",\n\t\t\"displayName\": \"Tidefi\",\n\t\t\"symbols\": [\n\t\t\t\"TDFY\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://tidefi.com\"\n\t},\n\t{\n\t\t\"prefix\": 7013,\n\t\t\"network\": \"gm\",\n\t\t\"displayName\": \"GM\",\n\t\t\"symbols\": [\n\t\t\t\"FREN\",\n\t\t\t\"GM\",\n\t\t\t\"GN\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12,\n\t\t\t0,\n\t\t\t0\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://gmordie.com\"\n\t},\n\t{\n\t\t\"prefix\": 7306,\n\t\t\"network\": \"krigan\",\n\t\t\"displayName\": \"Krigan Network\",\n\t\t\"symbols\": [\n\t\t\t\"KRGN\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t9\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://krigan.network\"\n\t},\n\t{\n\t\t\"prefix\": 7391,\n\t\t\"network\": \"unique_mainnet\",\n\t\t\"displayName\": \"Unique Network\",\n\t\t\"symbols\": [\n\t\t\t\"UNQ\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://unique.network\"\n\t},\n\t{\n\t\t\"prefix\": 8866,\n\t\t\"network\": \"golden_gate\",\n\t\t\"displayName\": \"Golden Gate\",\n\t\t\"symbols\": [\n\t\t\t\"GGX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://ggxchain.io/\"\n\t},\n\t{\n\t\t\"prefix\": 8883,\n\t\t\"network\": \"sapphire_mainnet\",\n\t\t\"displayName\": \"Sapphire by Unique\",\n\t\t\"symbols\": [\n\t\t\t\"QTZ\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://unique.network\"\n\t},\n\t{\n\t\t\"prefix\": 8886,\n\t\t\"network\": \"golden_gate_sydney\",\n\t\t\"displayName\": \"Golden Gate Sydney\",\n\t\t\"symbols\": [\n\t\t\t\"GGXT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://ggxchain.io/\"\n\t},\n\t{\n\t\t\"prefix\": 9072,\n\t\t\"network\": \"hashed\",\n\t\t\"displayName\": \"Hashed Network\",\n\t\t\"symbols\": [\n\t\t\t\"HASH\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://hashed.network\"\n\t},\n\t{\n\t\t\"prefix\": 9807,\n\t\t\"network\": \"dentnet\",\n\t\t\"displayName\": \"DENTNet\",\n\t\t\"symbols\": [\n\t\t\t\"DENTX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://www.dentnet.io\"\n\t},\n\t{\n\t\t\"prefix\": 9935,\n\t\t\"network\": \"t3rn\",\n\t\t\"displayName\": \"t3rn\",\n\t\t\"symbols\": [\n\t\t\t\"TRN\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://t3rn.io/\"\n\t},\n\t{\n\t\t\"prefix\": 10041,\n\t\t\"network\": \"basilisk\",\n\t\t\"displayName\": \"Basilisk\",\n\t\t\"symbols\": [\n\t\t\t\"BSX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://bsx.fi\"\n\t},\n\t{\n\t\t\"prefix\": 11330,\n\t\t\"network\": \"cess-testnet\",\n\t\t\"displayName\": \"CESS Testnet\",\n\t\t\"symbols\": [\n\t\t\t\"TCESS\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://cess.cloud\"\n\t},\n\t{\n\t\t\"prefix\": 11331,\n\t\t\"network\": \"cess\",\n\t\t\"displayName\": \"CESS\",\n\t\t\"symbols\": [\n\t\t\t\"CESS\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://cess.cloud\"\n\t},\n\t{\n\t\t\"prefix\": 11486,\n\t\t\"network\": \"luhn\",\n\t\t\"displayName\": \"Luhn Network\",\n\t\t\"symbols\": [\n\t\t\t\"LUHN\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://luhn.network\"\n\t},\n\t{\n\t\t\"prefix\": 11820,\n\t\t\"network\": \"contextfree\",\n\t\t\"displayName\": \"Automata ContextFree\",\n\t\t\"symbols\": [\n\t\t\t\"CTX\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://ata.network\"\n\t},\n\t{\n\t\t\"prefix\": 12155,\n\t\t\"network\": \"impact\",\n\t\t\"displayName\": \"Impact Protocol Network\",\n\t\t\"symbols\": [\n\t\t\t\"BSTY\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://impactprotocol.network/\"\n\t},\n\t{\n\t\t\"prefix\": 12191,\n\t\t\"network\": \"nftmart\",\n\t\t\"displayName\": \"NFTMart\",\n\t\t\"symbols\": [\n\t\t\t\"NMT\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://nftmart.io\"\n\t},\n\t{\n\t\t\"prefix\": 12850,\n\t\t\"network\": \"analog-timechain\",\n\t\t\"displayName\": \"Analog Timechain\",\n\t\t\"symbols\": [\n\t\t\t\"ANLOG\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://analog.one\"\n\t},\n\t{\n\t\t\"prefix\": 13116,\n\t\t\"network\": \"bittensor\",\n\t\t\"displayName\": \"Bittensor\",\n\t\t\"symbols\": [\n\t\t\t\"TAO\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t9\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://bittensor.com\"\n\t},\n\t{\n\t\t\"prefix\": 14697,\n\t\t\"network\": \"goro\",\n\t\t\"displayName\": \"GORO Network\",\n\t\t\"symbols\": [\n\t\t\t\"GORO\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t9\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://goro.network\"\n\t},\n\t{\n\t\t\"prefix\": 14998,\n\t\t\"network\": \"mosaic-chain\",\n\t\t\"displayName\": \"Mosaic Chain\",\n\t\t\"symbols\": [\n\t\t\t\"MOS\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://mosaicchain.io\"\n\t},\n\t{\n\t\t\"prefix\": 29972,\n\t\t\"network\": \"mythos\",\n\t\t\"displayName\": \"Mythos\",\n\t\t\"symbols\": [\n\t\t\t\"MYTH\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t18\n\t\t],\n\t\t\"standardAccount\": \"secp256k1\",\n\t\t\"website\": \"https://mythos.foundation\"\n\t},\n\t{\n\t\t\"prefix\": 8888,\n\t\t\"network\": \"xcavate\",\n\t\t\"displayName\": \"Xcavate Protocol\",\n\t\t\"symbols\": [\n\t\t\t\"XCAV\"\n\t\t],\n\t\t\"decimals\": [\n\t\t\t12\n\t\t],\n\t\t\"standardAccount\": \"*25519\",\n\t\t\"website\": \"https://xcavate.io/\"\n\t}\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@substrate/ss58-registry/esm/index.js\n");

/***/ })

};
;